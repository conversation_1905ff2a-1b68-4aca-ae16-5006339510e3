---
title: Get A Currency
description: GET /{companyId}/currencies/{currencyId}
---

## Get a currency

### Request endpoint

```http
GET /{companyId}/currencies/{currencyId}
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter  | Type          | Required | Description                   |
|------------|---------------|----------|-------------------------------|
| companyId  | string <uuid> | yes      | Your inFlow account companyId |
| currencyId | string <uuid> | yes      | Id of the Currency to retrieve |

### Response

#### Success response (200) schema: `application/json`

| Field               | Type                     | Description                  |
|---------------------|--------------------------|------------------------------|
| currencyConversions | `Array of objects`       | A list of conversion rates related to this currency. |
| currencyId          | `string <uuid>`          | The primary identifier for this currency. Not shown to users |
| decimalPlaces       | `integer <int32>`        | The number of decimal places typically shown with this currency |
| decimalSeparator    | `string`                 | The symbol used to separate decimals with this currency |
| isSymbolFirst       | `boolean`                | Whether the symbol is shown prior to the numerical value for this currency |
| isoCode             | `string`                 | The ISO 4217 code for this currency |
| name                | `string`                 | A descriptive name of this currency |
| negativeType        | `string` of enum         | How negative numbers are shown for this currency |
| symbol              | `string`                 | A short symbol representing this currency |
| thousandsSeparator  | `string`                 | The symbol used to separate thousands with this currency |
| timestamp           | `string <rowversion>`    | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

Valid values for `negativeType`:

- `Leading`
- `LeadingInsideSymbol`
- `TrailingInsideSymbol`
- `Trailing`
- `Bracketed`

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "currencyConversions": [
    {
      "currency": {},
      "currencyConversionId": "string",
      "currencyId": "00000000-0000-0000-0000-000000000000",
      "exchangeRate": "1.29",
      "isManual": true,
      "timestamp": "0000000000310AB6"
    }
  ],
  "currencyId": "00000000-0000-0000-0000-000000000000",
  "decimalPlaces": 2,
  "decimalSeparator": ".",
  "isSymbolFirst": true,
  "isoCode": "USD",
  "name": "US Dollar",
  "negativeType": "Leading",
  "symbol": "$",
  "thousandsSeparator": ",",
  "timestamp": "0000000000310AB6"
}
