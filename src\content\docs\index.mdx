---
title: Inflow Inventory API Documentation
description: Complete API reference for Inflow Inventory - manage products, orders, stock, and more programmatically.
template: splash
hero:
  tagline: Complete API reference for Inflow Inventory management
  actions:
    - text: Get Started
      link: /overview/
      icon: right-arrow
    - text: View API Endpoints
      link: /product/
      icon: document
      variant: minimal
---

import { Card, CardGrid } from '@astrojs/starlight/components';

## Quick Start

<CardGrid stagger>
	<Card title="Getting Started" icon="rocket">
		Learn how to [get API access](/overview/) and authenticate your requests to start using the Inflow Inventory API.
	</Card>
	<Card title="Products & Inventory" icon="list-format">
		Manage your [products](/product/), track [inventory levels](/product/get-product-inventory-summary/), and handle [stock adjustments](/stock-adjustment/).
	</Card>
	<Card title="Orders & Transactions" icon="document">
		Create and manage [sales orders](/sales-order/), [purchase orders](/purchase-order/), and [stock transfers](/stock-transfer/).
	</Card>
	<Card title="Customers & Vendors" icon="contact">
		Maintain your [customer](/customer/) and [vendor](/vendor/) databases with full CRUD operations.
	</Card>
</CardGrid>

## Popular Endpoints

<CardGrid>
	<Card title="List Products" icon="list-format">
		[Get all products](/product/list-products/) in your inventory with filtering and pagination support.
	</Card>
	<Card title="Create Sales Order" icon="add-document">
		[Insert or update sales orders](/sales-order/insert-or-update-sales-order/) to manage your sales workflow.
	</Card>
	<Card title="Stock Adjustments" icon="setting">
		[Adjust inventory levels](/stock-adjustment/insert-or-update-a-stock-adjustment/) for accurate stock management.
	</Card>
	<Card title="Customer Management" icon="contact">
		[Manage customer data](/customer/insert-or-update-a-customer/) including contact details and custom fields.
	</Card>
</CardGrid>
