## Get a location

Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/locations/{locationId}
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter  | Type          | Required | Description                   |
|------------|---------------|----------|-------------------------------|
| companyId  | string <uuid> | yes      | Your inFlow account companyId |
| locationId | string <uuid> | yes      | Id of the Location to retrieve |

### Response

#### Success response (200) schema: `application/json`

| Field      | Type                | Description                        |
|------------|---------------------|------------------------------------|
| address    | object (Address)    |                                    |
| isActive   | boolean             | Locations with `IsActive = false` are deactivated and hidden away for new usage, but inventory is not removed from that location. |
| isDefault  | boolean             | Only one location, your company-wide default, should have `IsDefault = true`. |
| locationId | string <uuid>       | The primary identifier for this location. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| name       | string              | Human-readable name for this location. A location most typically represents a warehouse or store. |
| timestamp  | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "address": {
    "address1": "36 Wonderland Ave.",
    "address2": "Unit 207",
    "addressType": "Commercial",
    "city": "Toronto",
    "country": "Canada",
    "postalCode": "90210",
    "remarks": "string",
    "state": "Ontario"
  },
  "isActive": true,
  "isDefault": true,
  "locationId": "00000000-0000-0000-0000-000000000000",
  "name": "string",
  "timestamp": "0000000000310AB6"
}
```
