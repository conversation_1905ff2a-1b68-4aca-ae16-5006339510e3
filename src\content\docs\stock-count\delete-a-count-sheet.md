---
title: Delete A Count Sheet
description: DELETE /{companyId}/stock-counts/{stockCountId}/count-sheets/{countSheetId}
---

## Delete a count sheet

### Request endpoint

```http
DELETE /{companyId}/stock-counts/{stockCountId}/count-sheets/{countSheetId}
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter    | Type          | Required | Description                    |
|--------------|---------------|----------|--------------------------------|
| companyId    | string <uuid> | Yes      | Your inFlow account companyId  |
| stockCountId | string <uuid> | Yes      | The stockCountId which contains the sheet to be deleted |
| countSheetId | string <uuid> | Yes      | The countSheetId to be deleted |

### Response

#### Success response (200) schema: `application/json`

| Field                     | Type                     | Description       |
|---------------------------|--------------------------|-------------------|
| assignedToTeamMember      | object (TeamMember)      |                   |
| assignedToTeamMemberId    | string <uuid> (Nullable) |                   |
| completedDate             | string <date-time> (Nullable) | The date this stock count was completed |
| isCancelled               | boolean                  | Whether this count sheet is cancelled (being cancelled voids inventory adjustments) |
| isCompleted               | boolean                  | Whether this stock count is completed (inventory adjustmentments are made when completed) |
| isPrepared                | boolean                  | Whether or not the user has gone through the prepare count modal |
| isReviewed                | boolean                  | Whether this stock count has been reviewed |
| isStarted                 | boolean                  | Whether this stock count is ready for counting |
| lastModifiedBy            | object (TeamMember)      |                   |
| lastModifiedById          | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this stock count. This is set automatically, and cannot be set through the API. |
| location                  | object (Location)        |                   |
| locationId                | string <uuid>            |                   |
| remarks                   | string                   | Any extra comments on this stock count |
| sheets                    | Array of objects         | Count sheets that are part of this stock count |
| startedDate               | string <date-time> (Nullable) | The date this stock count was started |
| status                    | string                   | The status of this stock count <br> Enum: "Open" "InProgress" "InReview" "Completed" |
| stockCountId              | string <uuid>            | The primary identifier for this stock count. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| stockCountNumber          | string                   | The number of this stock count |
| timestamp                 | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 2595 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-delete-a-count-sheet.json](response-sample-of-delete-a-count-sheet.json)
