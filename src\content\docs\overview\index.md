---
title: Index
description: The inFlow API allows you to read and write data to your inFlow. Like most modern APIs, it's organized around REST and JSON, but there are some concepts to learn.
---

# [Overview](#Overview)

## [Introduction](#Introduction)
The inFlow API allows you to read and write data to your inFlow. Like most modern APIs, it's organized around REST and JSON, but there are some concepts to learn.

## [Getting access to the API](#Getting-access-to-the-API)
You will need:
- an active inFlow account with the API access add-on, or an inFlow trial
- administrator rights to that account

Then, you can go to [https://app.inflowinventory.com/options/integrations](https://app.inflowinventory.com/options/integrations) and generate an API key. This API key grants access to your inFlow account (until revoked), so please keep it safe! You will also need your `companyId`, an identifier for your inFlow account, which is also available on that same page in the API keys section once an API key has been generated.

## [Support](#Support)
If you need help, you can email us at [<EMAIL>](mailto:<EMAIL>).

Although we will try to maintain API stability and reliability, we cannot make any guarantees.

## [Endpoint](#Endpoint)
The base URL for the inFlow API is [https://cloudapi.inflowinventory.com/](https://cloudapi.inflowinventory.com/). HTTPS is required for security. Most API calls will also need your `companyId` in the route. The API does not support CORS for browser apps.

## [HTTP headers fields](#HTTP-headers-fields)
You should set (at least) the following 3 HTTP header fields in your inFlow API calls (other headers may be set optionally, see below for more details)

`Authorization: Bearer {YOUR_API_KEY_HERE}`

`Content-Type: application/json`

`Accept: application/json;version=2025-06-24`

The current API version is `2025-06-24`, which should be included in the Accept HTTP header. From time to time, we may need to make breaking changes on our API. Prior to this, if you have generated an API key, we will email all administrators of your inFlow account with information about this change.

## [Optional headers fields](#Optional-headers-fields)
There are some headers/tags you can send with your requests that can change default behavior of the API.

`X-OverrideAllowNegativeInventory: TRUE`

Setting this to true, will allow picking an amount of products that would make inventory levels in that location go into a negative quantity. By default this is `FALSE` and you will receive an error `422 - Negative Inventory` if not overridden.

## [Read requests](#Read-requests)
inFlow API read requests use the HTTP GET verb and return one or more entities in JSON format.

### Including related entities
By default, API calls to fetch an entity (e.g. a product) do not automatically fetch related entities (e.g. product prices or images). You can specify which entities to include by including a query parameter `Include`, separating the requested relationship entities with a comma. You can also specify nested relationships with a period.

e.g. `?include=inventoryLines.location,defaultImage`

### Filtering
Most API calls that return multiple entities offer several filtering options. Adding these to the query parameters will filter the returned results.

e.g. `?filter[isActive]=true&filter[name]=shirt`

## [Write requests](#Write-requests)
inFlow API write requests typically use the PUT HTTP verb and can be used for either inserting or updating entities from the body of the HTTP request.

### Inserting, updating, and identifiers
inFlow entities use a globally unique identifier (GUID), also called a universally unique identifier (UUID). You should generate a new GUID when calling the API to insert a new entity (most programming languages will have a built-in or add-on package to do so), or reference an existing GUID to update an entity.

### Optional properties
Most entity properties are optional, and any missing properties will be set to default values when inserting, or left as-is when updating.

### Timestamps
Many inFlow entities include a `timestamp` property. This is a machine-readable stamp of when the entity was last modified which can be used for concurrency. If you include the timestamp property in your API write request, your request will fail if the timestamp is no longer the latest version, protecting you against making unintended modifications. To disable this feature, exclude the timestamp property in your API write request.

## [Rate limiting](#Rate-limiting)
If you send too many API requests in a short time, the inFlow API will return HTTP status code `429 Too Many Requests`.

If you encounter this limit, we encourage you to try to reduce the number of API calls required, or contact us for help if you are unable to do so.

Currently, the limit is set to 60 requests per minute but this is not yet finalized and may change.
