---
title: List Purchase Orders
description: Relationships can be included via the include query parameter.
---

## List purchase orders

Relationships can be included via the `include` query parameter.  

Options for filtering this list:
- `filter[orderNumber]`
- `filter[inventoryStatus] array of statuses`
- `filter[paymentStatus]` array of statuses
- `filter[vendorId]`
- `filter[orderDate]` date range object with `fromDate` and `toDate`
- `filter[vendorOrderNumber]`
- `filter[locationId]`
- `filter[requestShipDate]` date range object with `fromDate` and `toDate`
- `filter[total]` number range object with `fromDate` and `toDate`
- `filter[balance]` number range object with `fromDate` and `toDate`
- `filter[isActive]`
- `filter[smart]` (search on order number and vendor name)

### Request endpoint

```http
GET /{companyId}/purchase-orders
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | Yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of PurchaseOrder objects. Each PurchaseOrder object has the following properties:

| Field                  | Type                     | Description      |
|------------------------|--------------------------|------------------|
| amountPaid             | string <decimal>         | The amount that you have paid this vendor. |
| approverTeamMember     | object (TeamMember)      |                  |
| approverTeamMemberId   | string <uuid> (Nullable) |                  |
| assignedToTeamMember   | object (TeamMember)      |                  |
| assignedToTeamMemberId | string <uuid> (Nullable) |                  |
| balance                | string <decimal>         | The remaining amount that you owe this vendor. |
| calculateTax2OnTax1    | boolean                  | Whether a secondary tax should be compounded on top of the primary tax |
| carrier                | string                   | The carrier or shipping method for this order |
| contactName            | string                   | The name of the vendor's employee that you should contact for this order |
| currency               | object (Currency)        |                  |
| currencyId             | string <uuid>            |                  |
| customFields           | object (LargeCustomFieldValues) |           |
| dueDate                | string <date-time> (Nullable) | The date by which payment is due |
| email                  | string                   | The email address for the vendor that you should contact for this order |
| exchangeRate           | string <decimal>         | The exchange rate between the currency in this order and your home currency effective for this order |
| exchangeRateAutoPulled | string <date-time> (Nullable) | If this exchange rate was automatically pulled, then the date it was set, otherwise null. |
| freight                | string <decimal> (Nullable) | The amount this vendor charges you for shipping |
| inventoryStatus        | string                   | The inventory-related status of this order<br>Enum: "Quote", "Unapproved", "Unfulfilled", "Started", "Fulfilled" |
| isCancelled            | boolean                  | Whether this order is cancelled (being cancelled voids any payments and inventory movements) |
| isCompleted            | boolean                  | Whether this order is completed (fully received) |
| isQuote                | boolean                  | Whether this order is a quote |
| isTaxInclusive         | boolean                  | When `true`, then prices should be treated as tax-inclusive. |
| lastModifiedBy         | object (TeamMember)      |                  |
| lastModifiedById       | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this purchase order. This is set automatically, and cannot be set through the API. |
| lines                  | Array of objects         | Lines representing which goods have been ordered and returned |
| location               | object (Location)        |                  |
| locationId             | string <uuid> (Nullable) |                  |
| nonVendorCosts         | object (PercentOrFixedAmount) |             |
| orderDate              | string <date-time>       | The date this order was placed. |
| orderNumber            | string                   | An identifier for this purchase order and shown on printed documents. |
| orderRemarks           | string                   | Any extra comments on this order |
| paymentLines           | Array of objects         | Lines representing a history of payment details for this order. |
| paymentStatus          | string                   | The total amount the customer should pay, including taxes and shipping<br>Enum: "Quote", "Unapproved", "Unpaid", "Partial", "Paid", "Owing" |
| paymentTerms           | object (PaymentTerms)    |                  |
| paymentTermsId         | string <uuid> (Nullable) |                  |
| phone                  | string                   | The phone number for the vendor that you should contact for this order |
| purchaseOrderId        | string <uuid>            | The primary identifier for this purchase order. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| receiveLines           | Array of objects         | Lines representing which goods have been received into your warehouse |
| receiveRemarks         | string                   | Any extra comments on this order regarding receiving |
| requestShipDate        | string <date-time> (Nullable) | The date that you request that this order be shipped |
| returnExtra            | string <decimal>         | The amount that this vendor refunds you for returns related to shipping |
| returnFee              | string <decimal>         | The amount this vendor charges you for return fees |
| returnRemarks          | string                   | Any extra comments on this order regarding returns |
| shipToAddress          | object (Address)         |                  |
| shipToCompanyName      | string                   | The ship-to company name shown on printed documents, e.g. for dropshipping |
| showShipping           | boolean                  | Whether this order will be shipped; this controls whether certain fields will be shown. |
| subTotal               | string <decimal>         | The total of line items for this order |
| tax1                   | string <decimal>         | The calculated primary tax amount for this order |
| tax1Name               | string                   | A short name for display of the primary tax |
| tax1OnShipping         | boolean                  | Whether the primary tax applies to shipping/freight costs |
| tax1Rate               | string <decimal> (Nullable) | The default percentage primary tax for this order. |
| tax2                   | string <decimal>         | The calculated secondary tax amount for this order |
| tax2Name               | string                   | A short name for display of the secondary tax |
| tax2OnShipping         | boolean                  | Whether the secondary tax applies to shipping/freight costs |
| tax2Rate               | string <decimal> (Nullable) | The default percentage secondary tax for this order. |
| taxingScheme           | object (TaxingScheme)    |                  |
| taxingSchemeId         | string <uuid>            |                  |
| timestamp              | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| total                  | string <decimal>         | The total amount you should pay, including taxes and shipping |
| unstockLines           | Array of objects         | Lines representing which returned items have been unstocked |
| unstockRemarks         | string                   | Any extra comments on this order regarding unstocking |
| vendor                 | object (Vendor)          |                  |
| vendorAddress          | object (Address)         |                  |
| vendorId               | string <uuid>            |                  |
| vendorOrderNumber      | string                   | The vendor's number for this order. |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 1255 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-list-purchase-orders.json](response-sample-of-list-purchase-orders.json)
