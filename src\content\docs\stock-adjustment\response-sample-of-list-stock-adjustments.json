[{"adjustmentNumber": "SA-000123", "adjustmentReason": {"adjustmentReasonId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isInternal": true, "name": "string"}, "adjustmentReasonId": "00000000-0000-0000-0000-000000000000", "customFields": {"custom1": "string", "custom10": "string", "custom2": "string", "custom3": "string", "custom4": "string", "custom5": "string", "custom6": "string", "custom7": "string", "custom8": "string", "custom9": "string"}, "date": "2020-01-31", "isCancelled": true, "lastModifiedBy": {"accessAllLocations": true, "accessLocationIds": ["00000000-0000-0000-0000-000000000000"], "accessRights": ["SALES_SalesOrder_View"], "canBeSalesRep": true, "email": "string", "isActive": true, "isInternal": true, "name": "<PERSON>", "teamMemberId": "00000000-0000-0000-0000-000000000000"}, "lastModifiedById": "00000000-0000-0000-0000-000000000000", "lines": [{"description": "string", "lot": {"createdDate": "2020-01-31", "customFields": {"comparer": {}, "count": "100", "item": "string", "keys": {"count": "100"}, "values": {"count": "100"}}, "expiryDate": "2020-01-31", "lotId": "00000000-0000-0000-0000-000000000000", "lotNumber": "string", "manufactureDate": "2020-01-31", "product": {"autoAssemble": true, "category": {"categoryId": "00000000-0000-0000-0000-000000000000", "isDefault": true, "name": "Bestsellers", "parentCategory": {}, "parentCategoryId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "categoryId": "00000000-0000-0000-0000-000000000000", "cost": {"cost": "19.99", "product": {}, "productCostId": "string", "productId": "00000000-0000-0000-0000-000000000000"}, "customFields": {"custom1": "string", "custom10": "string", "custom2": "string", "custom3": "string", "custom4": "string", "custom5": "string", "custom6": "string", "custom7": "string", "custom8": "string", "custom9": "string"}, "defaultImage": {"imageId": "00000000-0000-0000-0000-000000000000", "largeUrl": "string", "mediumUncroppedUrl": "string", "mediumUrl": "string", "originalUrl": "string", "smallUrl": "string", "thumbUrl": "string"}, "defaultImageId": "00000000-0000-0000-0000-000000000000", "defaultPrice": {"fixedMarkup": "19.99", "priceType": "FixedPrice", "pricingScheme": {"currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "isTaxInclusive": true, "name": "Retail price", "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "productPrices": [{"fixedMarkup": "19.99", "priceType": "FixedPrice", "pricingScheme": {}, "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productPriceId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "unitPrice": "19.99"}], "timestamp": "0000000000310AB6"}, "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productPriceId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "unitPrice": "19.99"}, "description": "Red toy sports car", "height": "19.99", "hsTariffNumber": "string", "images": [{"imageId": "00000000-0000-0000-0000-000000000000", "largeUrl": "string", "mediumUncroppedUrl": "string", "mediumUrl": "string", "originalUrl": "string", "smallUrl": "string", "thumbUrl": "string"}], "includeQuantityBuildable": true, "inventoryLines": [{"inventoryLineId": "string", "location": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "isActive": true, "isDefault": true, "locationId": "00000000-0000-0000-0000-000000000000", "name": "string", "timestamp": "0000000000310AB6"}, "locationId": "00000000-0000-0000-0000-000000000000", "lot": {}, "lotId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "quantityOnHand": "19.99", "serial": "string", "sublocation": "A-12-B", "timestamp": "0000000000310AB6"}], "isActive": true, "isManufacturable": true, "itemBoms": [{"childProduct": {}, "childProductId": "00000000-0000-0000-0000-000000000000", "itemBomId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "quantity": {"serialNumbers": ["string"], "standardQuantity": "19.99", "uom": "cases", "uomQuantity": "19.99"}, "timestamp": "0000000000310AB6"}], "itemType": "StockedProduct", "lastModifiedBy": {"accessAllLocations": true, "accessLocationIds": ["00000000-0000-0000-0000-000000000000"], "accessRights": ["SALES_SalesOrder_View"], "canBeSalesRep": true, "email": "string", "isActive": true, "isInternal": true, "name": "<PERSON>", "teamMemberId": "00000000-0000-0000-0000-000000000000"}, "lastModifiedById": "00000000-0000-0000-0000-000000000000", "lastModifiedDateTime": "2020-01-31", "lastVendor": {"addresses": [{"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}], "balances": [{"balance": "500.00", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorBalanceId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "contactName": "<PERSON>", "credits": [{"credit": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorCreditId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "customFields": {"custom1": "string", "custom10": "string", "custom2": "string", "custom3": "string", "custom4": "string", "custom5": "string", "custom6": "string", "custom7": "string", "custom8": "string", "custom9": "string"}, "defaultAddress": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}, "defaultAddressId": "00000000-0000-0000-0000-000000000000", "defaultCarrier": "FedEx", "defaultPaymentMethod": "Mastercard", "defaultPaymentTerms": {"daysDue": 30, "isActive": true, "name": "NET 30", "paymentTermsId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultPaymentTermsId": "00000000-0000-0000-0000-000000000000", "discount": "10", "dues": [{"amount1To30": "19.99", "amount31To60": "19.99", "amount61Plus": "19.99", "amountCurrent": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendorDueId": "string"}], "email": "<EMAIL>", "fax": "************", "isActive": true, "isTaxInclusivePricing": true, "lastModifiedBy": {"accessAllLocations": true, "accessLocationIds": ["00000000-0000-0000-0000-000000000000"], "accessRights": ["SALES_SalesOrder_View"], "canBeSalesRep": true, "email": "string", "isActive": true, "isInternal": true, "name": "<PERSON>", "teamMemberId": "00000000-0000-0000-0000-000000000000"}, "lastModifiedById": "00000000-0000-0000-0000-000000000000", "lastModifiedDttm": "2020-01-31", "leadTimeDays": 14, "name": "Acme Widget Co.", "phone": "************", "remarks": "string", "taxingScheme": {"calculateTax2OnTax1": true, "defaultTaxCode": {"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "name": "NYC sales tax", "tax1Name": "VAT", "tax1OnShipping": true, "tax2Name": "PST", "tax2OnShipping": true, "taxCodes": [{"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItems": [{"cost": "19.99", "leadTimeDays": "100", "lineNum": "100", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendor": {}, "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItemCode": "ABC-123", "vendorItemId": "00000000-0000-0000-0000-000000000000"}], "website": "www.acmewidget.com"}, "lastVendorId": "00000000-0000-0000-0000-000000000000", "length": "19.99", "name": "1948 6\" Toy Car - Red", "originCountry": "string", "prices": [{"fixedMarkup": "19.99", "priceType": "FixedPrice", "pricingScheme": {"currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "isTaxInclusive": true, "name": "Retail price", "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "productPrices": [{"fixedMarkup": "19.99", "priceType": "FixedPrice", "pricingScheme": {}, "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productPriceId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "unitPrice": "19.99"}], "timestamp": "0000000000310AB6"}, "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productPriceId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "unitPrice": "19.99"}], "productBarcodes": [{"barcode": "string", "lineNum": "100", "product": {}, "productBarcodeId": "00000000-0000-0000-0000-000000000000", "productId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "productId": "00000000-0000-0000-0000-000000000000", "productOperations": [{"cost": "19.99", "estimatedMinutes": "19.99", "estimatedSeconds": "19.99", "instructions": "string", "lineNum": "100", "operationType": {"isActive": true, "isDefault": true, "name": "Assembly", "operationTypeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "operationTypeId": "00000000-0000-0000-0000-000000000000", "perHourCost": "19.99", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productOperationId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "purchasingUom": {"conversionRatio": {"standardQuantity": "19.99", "uomQuantity": "19.99"}, "name": "string"}, "remarks": "string", "reorderSettings": [{"defaultSublocation": "string", "enableReordering": true, "fromLocation": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "isActive": true, "isDefault": true, "locationId": "00000000-0000-0000-0000-000000000000", "name": "string", "timestamp": "0000000000310AB6"}, "fromLocationId": "00000000-0000-0000-0000-000000000000", "location": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "isActive": true, "isDefault": true, "locationId": "00000000-0000-0000-0000-000000000000", "name": "string", "timestamp": "0000000000310AB6"}, "locationId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "reorderMethod": "PurchaseOrder", "reorderPoint": "19.99", "reorderQuantity": "19.99", "reorderSettingsId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendor": {"addresses": [{"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}], "balances": [{"balance": "500.00", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorBalanceId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "contactName": "<PERSON>", "credits": [{"credit": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorCreditId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "customFields": {"custom1": "string", "custom10": "string", "custom2": "string", "custom3": "string", "custom4": "string", "custom5": "string", "custom6": "string", "custom7": "string", "custom8": "string", "custom9": "string"}, "defaultAddress": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}, "defaultAddressId": "00000000-0000-0000-0000-000000000000", "defaultCarrier": "FedEx", "defaultPaymentMethod": "Mastercard", "defaultPaymentTerms": {"daysDue": 30, "isActive": true, "name": "NET 30", "paymentTermsId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultPaymentTermsId": "00000000-0000-0000-0000-000000000000", "discount": "10", "dues": [{"amount1To30": "19.99", "amount31To60": "19.99", "amount61Plus": "19.99", "amountCurrent": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendorDueId": "string"}], "email": "<EMAIL>", "fax": "************", "isActive": true, "isTaxInclusivePricing": true, "lastModifiedBy": {"accessAllLocations": true, "accessLocationIds": ["00000000-0000-0000-0000-000000000000"], "accessRights": ["SALES_SalesOrder_View"], "canBeSalesRep": true, "email": "string", "isActive": true, "isInternal": true, "name": "<PERSON>", "teamMemberId": "00000000-0000-0000-0000-000000000000"}, "lastModifiedById": "00000000-0000-0000-0000-000000000000", "lastModifiedDttm": "2020-01-31", "leadTimeDays": 14, "name": "Acme Widget Co.", "phone": "************", "remarks": "string", "taxingScheme": {"calculateTax2OnTax1": true, "defaultTaxCode": {"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "name": "NYC sales tax", "tax1Name": "VAT", "tax1OnShipping": true, "tax2Name": "PST", "tax2OnShipping": true, "taxCodes": [{"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItems": [{"cost": "19.99", "leadTimeDays": "100", "lineNum": "100", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendor": {}, "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItemCode": "ABC-123", "vendorItemId": "00000000-0000-0000-0000-000000000000"}], "website": "www.acmewidget.com"}, "vendorId": "00000000-0000-0000-0000-000000000000"}], "salesUom": {"conversionRatio": {"standardQuantity": "19.99", "uomQuantity": "19.99"}, "name": "string"}, "sku": "CAR-1948-R", "standardUomName": "\"ea.\"", "taxCodes": [{"product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productTaxCodeId": "00000000-0000-0000-0000-000000000000", "taxCode": {"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {"calculateTax2OnTax1": true, "defaultTaxCode": {}, "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "name": "NYC sales tax", "tax1Name": "VAT", "tax1OnShipping": true, "tax2Name": "PST", "tax2OnShipping": true, "taxCodes": [{"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {"calculateTax2OnTax1": true, "defaultTaxCode": {"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "name": "NYC sales tax", "tax1Name": "VAT", "tax1OnShipping": true, "tax2Name": "PST", "tax2OnShipping": true, "taxCodes": [{"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "timestamp": "0000000000310AB6", "totalQuantityOnHand": "19.99", "trackSerials": true, "vendorItems": [{"cost": "19.99", "leadTimeDays": "100", "lineNum": "100", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendor": {"addresses": [{"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}], "balances": [{"balance": "500.00", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorBalanceId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "contactName": "<PERSON>", "credits": [{"credit": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorCreditId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "customFields": {"custom1": "string", "custom10": "string", "custom2": "string", "custom3": "string", "custom4": "string", "custom5": "string", "custom6": "string", "custom7": "string", "custom8": "string", "custom9": "string"}, "defaultAddress": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}, "defaultAddressId": "00000000-0000-0000-0000-000000000000", "defaultCarrier": "FedEx", "defaultPaymentMethod": "Mastercard", "defaultPaymentTerms": {"daysDue": 30, "isActive": true, "name": "NET 30", "paymentTermsId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultPaymentTermsId": "00000000-0000-0000-0000-000000000000", "discount": "10", "dues": [{"amount1To30": "19.99", "amount31To60": "19.99", "amount61Plus": "19.99", "amountCurrent": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendorDueId": "string"}], "email": "<EMAIL>", "fax": "************", "isActive": true, "isTaxInclusivePricing": true, "lastModifiedBy": {"accessAllLocations": true, "accessLocationIds": ["00000000-0000-0000-0000-000000000000"], "accessRights": ["SALES_SalesOrder_View"], "canBeSalesRep": true, "email": "string", "isActive": true, "isInternal": true, "name": "<PERSON>", "teamMemberId": "00000000-0000-0000-0000-000000000000"}, "lastModifiedById": "00000000-0000-0000-0000-000000000000", "lastModifiedDttm": "2020-01-31", "leadTimeDays": 14, "name": "Acme Widget Co.", "phone": "************", "remarks": "string", "taxingScheme": {"calculateTax2OnTax1": true, "defaultTaxCode": {"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "name": "NYC sales tax", "tax1Name": "VAT", "tax1OnShipping": true, "tax2Name": "PST", "tax2OnShipping": true, "taxCodes": [{"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItems": [{"cost": "19.99", "leadTimeDays": "100", "lineNum": "100", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendor": {}, "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItemCode": "ABC-123", "vendorItemId": "00000000-0000-0000-0000-000000000000"}], "website": "www.acmewidget.com"}, "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItemCode": "ABC-123", "vendorItemId": "00000000-0000-0000-0000-000000000000"}], "weight": "19.99", "width": "19.99"}, "productId": "00000000-0000-0000-0000-000000000000"}, "lotId": "00000000-0000-0000-0000-000000000000", "product": {"autoAssemble": true, "category": {"categoryId": "00000000-0000-0000-0000-000000000000", "isDefault": true, "name": "Bestsellers", "parentCategory": {}, "parentCategoryId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "categoryId": "00000000-0000-0000-0000-000000000000", "cost": {"cost": "19.99", "product": {}, "productCostId": "string", "productId": "00000000-0000-0000-0000-000000000000"}, "customFields": {"custom1": "string", "custom10": "string", "custom2": "string", "custom3": "string", "custom4": "string", "custom5": "string", "custom6": "string", "custom7": "string", "custom8": "string", "custom9": "string"}, "defaultImage": {"imageId": "00000000-0000-0000-0000-000000000000", "largeUrl": "string", "mediumUncroppedUrl": "string", "mediumUrl": "string", "originalUrl": "string", "smallUrl": "string", "thumbUrl": "string"}, "defaultImageId": "00000000-0000-0000-0000-000000000000", "defaultPrice": {"fixedMarkup": "19.99", "priceType": "FixedPrice", "pricingScheme": {"currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "isTaxInclusive": true, "name": "Retail price", "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "productPrices": [{"fixedMarkup": "19.99", "priceType": "FixedPrice", "pricingScheme": {}, "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productPriceId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "unitPrice": "19.99"}], "timestamp": "0000000000310AB6"}, "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productPriceId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "unitPrice": "19.99"}, "description": "Red toy sports car", "height": "19.99", "hsTariffNumber": "string", "images": [{"imageId": "00000000-0000-0000-0000-000000000000", "largeUrl": "string", "mediumUncroppedUrl": "string", "mediumUrl": "string", "originalUrl": "string", "smallUrl": "string", "thumbUrl": "string"}], "includeQuantityBuildable": true, "inventoryLines": [{"inventoryLineId": "string", "location": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "isActive": true, "isDefault": true, "locationId": "00000000-0000-0000-0000-000000000000", "name": "string", "timestamp": "0000000000310AB6"}, "locationId": "00000000-0000-0000-0000-000000000000", "lot": {"createdDate": "2020-01-31", "customFields": {"comparer": {}, "count": "100", "item": "string", "keys": {"count": "100"}, "values": {"count": "100"}}, "expiryDate": "2020-01-31", "lotId": "00000000-0000-0000-0000-000000000000", "lotNumber": "string", "manufactureDate": "2020-01-31", "product": {}, "productId": "00000000-0000-0000-0000-000000000000"}, "lotId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "quantityOnHand": "19.99", "serial": "string", "sublocation": "A-12-B", "timestamp": "0000000000310AB6"}], "isActive": true, "isManufacturable": true, "itemBoms": [{"childProduct": {}, "childProductId": "00000000-0000-0000-0000-000000000000", "itemBomId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "quantity": {"serialNumbers": ["string"], "standardQuantity": "19.99", "uom": "cases", "uomQuantity": "19.99"}, "timestamp": "0000000000310AB6"}], "itemType": "StockedProduct", "lastModifiedBy": {"accessAllLocations": true, "accessLocationIds": ["00000000-0000-0000-0000-000000000000"], "accessRights": ["SALES_SalesOrder_View"], "canBeSalesRep": true, "email": "string", "isActive": true, "isInternal": true, "name": "<PERSON>", "teamMemberId": "00000000-0000-0000-0000-000000000000"}, "lastModifiedById": "00000000-0000-0000-0000-000000000000", "lastModifiedDateTime": "2020-01-31", "lastVendor": {"addresses": [{"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}], "balances": [{"balance": "500.00", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorBalanceId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "contactName": "<PERSON>", "credits": [{"credit": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorCreditId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "customFields": {"custom1": "string", "custom10": "string", "custom2": "string", "custom3": "string", "custom4": "string", "custom5": "string", "custom6": "string", "custom7": "string", "custom8": "string", "custom9": "string"}, "defaultAddress": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}, "defaultAddressId": "00000000-0000-0000-0000-000000000000", "defaultCarrier": "FedEx", "defaultPaymentMethod": "Mastercard", "defaultPaymentTerms": {"daysDue": 30, "isActive": true, "name": "NET 30", "paymentTermsId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultPaymentTermsId": "00000000-0000-0000-0000-000000000000", "discount": "10", "dues": [{"amount1To30": "19.99", "amount31To60": "19.99", "amount61Plus": "19.99", "amountCurrent": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendorDueId": "string"}], "email": "<EMAIL>", "fax": "************", "isActive": true, "isTaxInclusivePricing": true, "lastModifiedBy": {"accessAllLocations": true, "accessLocationIds": ["00000000-0000-0000-0000-000000000000"], "accessRights": ["SALES_SalesOrder_View"], "canBeSalesRep": true, "email": "string", "isActive": true, "isInternal": true, "name": "<PERSON>", "teamMemberId": "00000000-0000-0000-0000-000000000000"}, "lastModifiedById": "00000000-0000-0000-0000-000000000000", "lastModifiedDttm": "2020-01-31", "leadTimeDays": 14, "name": "Acme Widget Co.", "phone": "************", "remarks": "string", "taxingScheme": {"calculateTax2OnTax1": true, "defaultTaxCode": {"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "name": "NYC sales tax", "tax1Name": "VAT", "tax1OnShipping": true, "tax2Name": "PST", "tax2OnShipping": true, "taxCodes": [{"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItems": [{"cost": "19.99", "leadTimeDays": "100", "lineNum": "100", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendor": {}, "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItemCode": "ABC-123", "vendorItemId": "00000000-0000-0000-0000-000000000000"}], "website": "www.acmewidget.com"}, "lastVendorId": "00000000-0000-0000-0000-000000000000", "length": "19.99", "name": "1948 6\" Toy Car - Red", "originCountry": "string", "prices": [{"fixedMarkup": "19.99", "priceType": "FixedPrice", "pricingScheme": {"currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "isTaxInclusive": true, "name": "Retail price", "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "productPrices": [{"fixedMarkup": "19.99", "priceType": "FixedPrice", "pricingScheme": {}, "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productPriceId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "unitPrice": "19.99"}], "timestamp": "0000000000310AB6"}, "pricingSchemeId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productPriceId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "unitPrice": "19.99"}], "productBarcodes": [{"barcode": "string", "lineNum": "100", "product": {}, "productBarcodeId": "00000000-0000-0000-0000-000000000000", "productId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "productId": "00000000-0000-0000-0000-000000000000", "productOperations": [{"cost": "19.99", "estimatedMinutes": "19.99", "estimatedSeconds": "19.99", "instructions": "string", "lineNum": "100", "operationType": {"isActive": true, "isDefault": true, "name": "Assembly", "operationTypeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "operationTypeId": "00000000-0000-0000-0000-000000000000", "perHourCost": "19.99", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productOperationId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "purchasingUom": {"conversionRatio": {"standardQuantity": "19.99", "uomQuantity": "19.99"}, "name": "string"}, "remarks": "string", "reorderSettings": [{"defaultSublocation": "string", "enableReordering": true, "fromLocation": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "isActive": true, "isDefault": true, "locationId": "00000000-0000-0000-0000-000000000000", "name": "string", "timestamp": "0000000000310AB6"}, "fromLocationId": "00000000-0000-0000-0000-000000000000", "location": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "isActive": true, "isDefault": true, "locationId": "00000000-0000-0000-0000-000000000000", "name": "string", "timestamp": "0000000000310AB6"}, "locationId": "00000000-0000-0000-0000-000000000000", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "reorderMethod": "PurchaseOrder", "reorderPoint": "19.99", "reorderQuantity": "19.99", "reorderSettingsId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendor": {"addresses": [{"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}], "balances": [{"balance": "500.00", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorBalanceId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "contactName": "<PERSON>", "credits": [{"credit": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorCreditId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "customFields": {"custom1": "string", "custom10": "string", "custom2": "string", "custom3": "string", "custom4": "string", "custom5": "string", "custom6": "string", "custom7": "string", "custom8": "string", "custom9": "string"}, "defaultAddress": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}, "defaultAddressId": "00000000-0000-0000-0000-000000000000", "defaultCarrier": "FedEx", "defaultPaymentMethod": "Mastercard", "defaultPaymentTerms": {"daysDue": 30, "isActive": true, "name": "NET 30", "paymentTermsId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultPaymentTermsId": "00000000-0000-0000-0000-000000000000", "discount": "10", "dues": [{"amount1To30": "19.99", "amount31To60": "19.99", "amount61Plus": "19.99", "amountCurrent": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendorDueId": "string"}], "email": "<EMAIL>", "fax": "************", "isActive": true, "isTaxInclusivePricing": true, "lastModifiedBy": {"accessAllLocations": true, "accessLocationIds": ["00000000-0000-0000-0000-000000000000"], "accessRights": ["SALES_SalesOrder_View"], "canBeSalesRep": true, "email": "string", "isActive": true, "isInternal": true, "name": "<PERSON>", "teamMemberId": "00000000-0000-0000-0000-000000000000"}, "lastModifiedById": "00000000-0000-0000-0000-000000000000", "lastModifiedDttm": "2020-01-31", "leadTimeDays": 14, "name": "Acme Widget Co.", "phone": "************", "remarks": "string", "taxingScheme": {"calculateTax2OnTax1": true, "defaultTaxCode": {"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "name": "NYC sales tax", "tax1Name": "VAT", "tax1OnShipping": true, "tax2Name": "PST", "tax2OnShipping": true, "taxCodes": [{"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItems": [{"cost": "19.99", "leadTimeDays": "100", "lineNum": "100", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendor": {}, "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItemCode": "ABC-123", "vendorItemId": "00000000-0000-0000-0000-000000000000"}], "website": "www.acmewidget.com"}, "vendorId": "00000000-0000-0000-0000-000000000000"}], "salesUom": {"conversionRatio": {"standardQuantity": "19.99", "uomQuantity": "19.99"}, "name": "string"}, "sku": "CAR-1948-R", "standardUomName": "\"ea.\"", "taxCodes": [{"product": {}, "productId": "00000000-0000-0000-0000-000000000000", "productTaxCodeId": "00000000-0000-0000-0000-000000000000", "taxCode": {"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {"calculateTax2OnTax1": true, "defaultTaxCode": {}, "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "name": "NYC sales tax", "tax1Name": "VAT", "tax1OnShipping": true, "tax2Name": "PST", "tax2OnShipping": true, "taxCodes": [{"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {"calculateTax2OnTax1": true, "defaultTaxCode": {"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "name": "NYC sales tax", "tax1Name": "VAT", "tax1OnShipping": true, "tax2Name": "PST", "tax2OnShipping": true, "taxCodes": [{"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "timestamp": "0000000000310AB6", "totalQuantityOnHand": "19.99", "trackSerials": true, "vendorItems": [{"cost": "19.99", "leadTimeDays": "100", "lineNum": "100", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendor": {"addresses": [{"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}], "balances": [{"balance": "500.00", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorBalanceId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "contactName": "<PERSON>", "credits": [{"credit": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendor": {}, "vendorCreditId": "string", "vendorId": "00000000-0000-0000-0000-000000000000"}], "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "customFields": {"custom1": "string", "custom10": "string", "custom2": "string", "custom3": "string", "custom4": "string", "custom5": "string", "custom6": "string", "custom7": "string", "custom8": "string", "custom9": "string"}, "defaultAddress": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "name": "string", "timestamp": "0000000000310AB6", "vendor": {}, "vendorAddressId": "00000000-0000-0000-0000-000000000000", "vendorId": "00000000-0000-0000-0000-000000000000"}, "defaultAddressId": "00000000-0000-0000-0000-000000000000", "defaultCarrier": "FedEx", "defaultPaymentMethod": "Mastercard", "defaultPaymentTerms": {"daysDue": 30, "isActive": true, "name": "NET 30", "paymentTermsId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultPaymentTermsId": "00000000-0000-0000-0000-000000000000", "discount": "10", "dues": [{"amount1To30": "19.99", "amount31To60": "19.99", "amount61Plus": "19.99", "amountCurrent": "19.99", "currency": {"currencyConversions": [{"currency": {}, "currencyConversionId": "string", "currencyId": "00000000-0000-0000-0000-000000000000", "exchangeRate": "1.29", "isManual": true, "timestamp": "0000000000310AB6"}], "currencyId": "00000000-0000-0000-0000-000000000000", "decimalPlaces": 2, "decimalSeparator": ".", "isSymbolFirst": true, "isoCode": "USD", "name": "US Dollar", "negativeType": "Leading", "symbol": "$", "thousandsSeparator": ",", "timestamp": "0000000000310AB6"}, "currencyId": "00000000-0000-0000-0000-000000000000", "vendorDueId": "string"}], "email": "<EMAIL>", "fax": "************", "isActive": true, "isTaxInclusivePricing": true, "lastModifiedBy": {"accessAllLocations": true, "accessLocationIds": ["00000000-0000-0000-0000-000000000000"], "accessRights": ["SALES_SalesOrder_View"], "canBeSalesRep": true, "email": "string", "isActive": true, "isInternal": true, "name": "<PERSON>", "teamMemberId": "00000000-0000-0000-0000-000000000000"}, "lastModifiedById": "00000000-0000-0000-0000-000000000000", "lastModifiedDttm": "2020-01-31", "leadTimeDays": 14, "name": "Acme Widget Co.", "phone": "************", "remarks": "string", "taxingScheme": {"calculateTax2OnTax1": true, "defaultTaxCode": {"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000", "isActive": true, "isDefault": true, "name": "NYC sales tax", "tax1Name": "VAT", "tax1OnShipping": true, "tax2Name": "PST", "tax2OnShipping": true, "taxCodes": [{"isActive": true, "name": "Taxable", "tax1Rate": "19.99", "tax2Rate": "19.99", "taxCodeId": "00000000-0000-0000-0000-000000000000", "taxingScheme": {}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}], "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}, "taxingSchemeId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItems": [{"cost": "19.99", "leadTimeDays": "100", "lineNum": "100", "product": {}, "productId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6", "vendor": {}, "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItemCode": "ABC-123", "vendorItemId": "00000000-0000-0000-0000-000000000000"}], "website": "www.acmewidget.com"}, "vendorId": "00000000-0000-0000-0000-000000000000", "vendorItemCode": "ABC-123", "vendorItemId": "00000000-0000-0000-0000-000000000000"}], "weight": "19.99", "width": "19.99"}, "productId": "00000000-0000-0000-0000-000000000000", "quantity": {"serialNumbers": ["string"], "standardQuantity": "19.99", "uom": "cases", "uomQuantity": "19.99"}, "stockAdjustmentLineId": "00000000-0000-0000-0000-000000000000", "sublocation": "C-19", "timestamp": "0000000000310AB6"}], "location": {"address": {"address1": "36 Wonderland Ave.", "address2": "Unit 207", "addressType": "Commercial", "city": "Toronto", "country": "Canada", "postalCode": "90210", "remarks": "string", "state": "Ontario"}, "isActive": true, "isDefault": true, "locationId": "00000000-0000-0000-0000-000000000000", "name": "string", "timestamp": "0000000000310AB6"}, "locationId": "00000000-0000-0000-0000-000000000000", "remarks": "string", "stockAdjustmentId": "00000000-0000-0000-0000-000000000000", "timestamp": "0000000000310AB6"}]