---
title: Get Multiple Product Inventory Summaries
description: POST /{companyId}/products/summary
---

## Get multiple product inventory summaries

### Request endpoint

```http
POST /{companyId}/products/summary
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

The product id, location id combinations to fetch information for. Up to 100 at a time

Array of objects. Each object has the following properties:

| Field                      | Type                     | Description  |
|----------------------------|--------------------------|--------------|
| productId                  | string <uuid>            |              |
| locationId                 | string <uuid> (Nullable) |              |

### Payload example

```json
[
  {
    "productId": "string",
    "locationId": "string"
  }
]
```

#### Success response (200) example

##### Content type: `application/json`

Array of objects. Each object has the following properties:

| Field                      | Type                       | Description  |
|----------------------------|----------------------------|--------------|
| productId                  | string <uuid>              | The product whose inventory is being represented here |
| locationId                 | string <uuid> (Nullable)   | The location whose inventory is being represented here, or null if this is a summary across all locations |
| imageSmallUrl              | string (Nullable)          | URL for a small image of the product |
| quantityOnHand             | number <double> (Nullable) | This number is the total you have physically available (including Qty reserved) minus any items that have already been picked in a sales order (i.e. what's still on your warehouse shelves). |
| quantityOnOrder            | number <double> (Nullable) | This number is how many you've ordered but haven't received. |
| quantityOnPurchaseOrder    | number <double> (Nullable) | A breakdown of QuantityOnOrder into only those ordered from vendors but haven't received. |
| quantityOnWorkOrder        | number <double> (Nullable) | A breakdown of QuantityOnOrder into only those on manufacture orders that haven't been finished yet. |
| quantityOnTransferOrder    | number <double> (Nullable) | A breakdown of QuantityOnOrder into only those expected to be received on a stock transfer (only when a locationId is specified). |
| quantityReserved           | number <double>            | This number is the total stock reserved for use that haven't been picked or used yet. |
| quantityReservedForSales   | number <double>            | A breakdown of QuantityReserved into only those reserved for sales orders. |
| quantityReservedForManufacturing | number <double>      | A breakdown of QuantityReserved into only those reserved for raw materials of manufacture orders. |
| quantityReservedForTransfers | number <double>          | A breakdown of QuantityReserved into only those reserved to be picked for a stock transfer (only when a locationId is specified). |
| quantityReservedForBuilds  | number <double>            | A breakdown of QuantityReserved into only those parts needed for bills of materials on products with negative anticipated inventory. |
| quantityAvailable          | number <double>            | This number is how many of the products you'll have left if you fulfill all open outgoing orders. This may include QuantityBuildable (for manufactured products set to combine quantities) and QuantityReservedForBuilds. |
| rawQuantityAvailable       | number <double>            | This number is how many of the products you'll have left if you fulfill all open outgoing orders, excluding QuantityBuildable and QuantityReservedForBuilds. |
| quantityPicked             | number <double> (Nullable) | This number is the total that has already been picked in sales orders/work orders and is awaiting shipment (think of them as sitting in a box waiting to be shipped out). |
| quantityInTransit          | number <double> (Nullable) | These are specific items that have been sent via Transfer Stock and are still in the Transit status (i.e. you've sent the transfer, but it has not been received at the other location yet). |
| quantityBuildable          | number <double> (Nullable) | For manufactured products, the quantity buildable is how many units can be built based on the stock of raw materials. If this is blank, then it means can infinite can be built (e.g. if it's composed only of services). |

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "productId": "string",
    "locationId": "string",
    "imageSmallUrl": "string",
    "quantityOnHand": 0,
    "quantityOnOrder": 0,
    "quantityOnPurchaseOrder": 0,
    "quantityOnWorkOrder": 0,
    "quantityOnTransferOrder": 0,
    "quantityReserved": 0,
    "quantityReservedForSales": 0,
    "quantityReservedForManufacturing": 0,
    "quantityReservedForTransfers": 0,
    "quantityReservedForBuilds": 0,
    "quantityAvailable": 0,
    "rawQuantityAvailable": 0,
    "quantityPicked": 0,
    "quantityInTransit": 0,
    "quantityBuildable": 0
  }
]
```
