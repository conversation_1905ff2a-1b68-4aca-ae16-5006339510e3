## Insert or update a stockroom scan

### Request endpoint

```http
PUT /{companyId}/stockroom-scans
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | Yes      | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

A stockroom scan to insert or update.

**Note**:
- `stockroomScanId` property is required, please generate a GUID when inserting.

| Field                  | Type                     | Description      |
|------------------------|--------------------------|------------------|
| attributes             | object (Nullable)        |                  |
| relationships          | object (Nullable)        |                  |
| meta                   | object (Nullable)        |                  |

### Payload example

```json
{
  "attributes": {
    "property1": {},
    "property2": {}
  },
  "relationships": {
    "property1": [
      {
        "meta": {
          "property1": {},
          "property2": {}
        }
      }
    ],
    "property2": [
      {
        "meta": {
          "property1": {},
          "property2": {}
        }
      }
    ]
  },
  "meta": {
    "property1": {},
    "property2": {}
  }
}
```

### Response

**200** Success

#### Response schema: application/json

| Field                  | Type                     | Description      |
|------------------------|--------------------------|------------------|
| attributes             | object (Nullable)        |                  |
| relationships          | object (Nullable)        |                  |
| meta                   | object (Nullable)        |                  |

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "attributes": {
    "property1": {},
    "property2": {}
  },
  "relationships": {
    "property1": [
      {
        "attributes": {
          "property1": {},
          "property2": {}
        },
        "relationships": {
          "property1": [
            {}
          ],
          "property2": [
            {}
          ]
        },
        "meta": {
          "property1": {},
          "property2": {}
        }
      }
    ],
    "property2": [
      {
        "attributes": {
          "property1": {},
          "property2": {}
        },
        "relationships": {
          "property1": [
            {}
          ],
          "property2": [
            {}
          ]
        },
        "meta": {
          "property1": {},
          "property2": {}
        }
      }
    ]
  },
  "meta": {
    "property1": {},
    "property2": {}
  }
}
```
