---
title: List Tax Codes
description: Relationships can be included via the include query parameter.
---

## List tax codes

Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/tax-codes
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of TaxCode objects. Each TaxCode object has the following properties:

| Field          | Type                  | Description                 |
|----------------|-----------------------|-----------------------------|
| isActive       | boolean               | Tax codes with `IsActive = false` are deactivated and hidden away for new usage. |
| name           | string                | Human-readable name for this tax code. |
| tax1Rate       | string <decimal>      | Percentage of the primary tax for this tax code. |
| tax2Rate       | string <decimal>      | Percentage of the secondary tax for this tax code. |
| taxCodeId      | string <uuid>         | The primary identifier for this tax code. **When inserting a tax code, you should specify this by generating a GUID**. Not shown to users |
| taxingScheme   | object (TaxingScheme) |                             |
| taxingSchemeId | string <uuid>         | The taxing scheme that this tax code is for |
| timestamp      | string <rowversion>   | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "isActive": true,
    "name": "Taxable",
    "tax1Rate": "19.99",
    "tax2Rate": "19.99",
    "taxCodeId": "00000000-0000-0000-0000-000000000000",
    "taxingScheme": {
      "calculateTax2OnTax1": true,
      "defaultTaxCode": {},
      "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000",
      "isActive": true,
      "isDefault": true,
      "name": "NYC sales tax",
      "tax1Name": "VAT",
      "tax1OnShipping": true,
      "tax2Name": "PST",
      "tax2OnShipping": true,
      "taxCodes": [
        {
          "isActive": true,
          "name": "Taxable",
          "tax1Rate": "19.99",
          "tax2Rate": "19.99",
          "taxCodeId": "00000000-0000-0000-0000-000000000000",
          "taxingScheme": {},
          "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
          "timestamp": "0000000000310AB6"
        }
      ],
      "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
      "timestamp": "0000000000310AB6"
    },
    "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
    "timestamp": "0000000000310AB6"
  }
]
```
